import re
import json
import time
import pickle
import requests
from pathlib import Path
from bs4 import <PERSON><PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>, Any
import undetected_chromedriver as uc
from quotexpy.exceptions import QuotexAuthError


class Browser(object):
    email = None
    password = None
    on_ping_code = None
    headless = None

    base_url = "market-qx.pro"
    https_base_url = f"https://{base_url}"

    def __init__(self, api):
        self.api = api

    def get_cookies_and_ssid(self) -> Tuple[Any, str]:
        try:
            self.browser = uc.Chrome(headless=self.headless, use_subprocess=False)
        except TypeError as exc:
            raise SystemError("Chrome is not installed, did you forget?") from exc
        self.browser.get(f"{self.https_base_url}/en/sign-in")
        if self.browser.current_url != f"{self.https_base_url}/en/trade":
            # Wait for page to load
            time.sleep(3)

            # Try multiple selectors for email field
            email_selectors = [
                'document.getElementsByName("email")[0]',
                'document.getElementsByName("email")[1]',
                'document.querySelector("input[name=email]")',
                'document.querySelector("input[type=email]")',
                'document.querySelector("#email")',
                'document.querySelector(".email-input")'
            ]

            email_set = False
            for selector in email_selectors:
                try:
                    self.browser.execute_script(f'if({selector}) {{ {selector}.value = arguments[0]; }}', self.email)
                    # Check if value was set
                    value = self.browser.execute_script(f'return {selector} ? {selector}.value : null;')
                    if value == self.email:
                        email_set = True
                        break
                except:
                    continue

            # Try multiple selectors for password field
            password_selectors = [
                'document.getElementsByName("password")[0]',
                'document.getElementsByName("password")[1]',
                'document.querySelector("input[name=password]")',
                'document.querySelector("input[type=password]")',
                'document.querySelector("#password")',
                'document.querySelector(".password-input")'
            ]

            password_set = False
            for selector in password_selectors:
                try:
                    self.browser.execute_script(f'if({selector}) {{ {selector}.value = arguments[0]; }}', self.password)
                    # Check if value was set
                    value = self.browser.execute_script(f'return {selector} ? {selector}.value : null;')
                    if value == self.password:
                        password_set = True
                        break
                except:
                    continue

            if not email_set or not password_set:
                raise QuotexAuthError(f"Could not find login fields. Email set: {email_set}, Password set: {password_set}")

            # Try multiple ways to submit the form
            submit_selectors = [
                """document.evaluate("//div[@id='tab-1']/form", document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue""",
                'document.querySelector("form")',
                'document.querySelector(".login-form")',
                'document.querySelector("#login-form")'
            ]

            submitted = False
            for selector in submit_selectors:
                try:
                    self.browser.execute_script(f'if({selector}) {{ {selector}.submit(); }}')
                    submitted = True
                    break
                except:
                    continue

            if not submitted:
                # Try clicking submit button
                try:
                    submit_btn = self.browser.find_element(uc.By.XPATH, "//button[@type='submit']")
                    submit_btn.click()
                except:
                    try:
                        submit_btn = self.browser.find_element(uc.By.CSS_SELECTOR, "button[type=submit]")
                        submit_btn.click()
                    except:
                        raise QuotexAuthError("Could not submit login form")

            time.sleep(5)

        try:
            code_input = self.browser.find_element(uc.By.NAME, "code")
            if code_input.is_displayed():
                code = self.on_ping_code()
                code_input.send_keys(code)
                btn = self.browser.find_element(uc.By.XPATH, "//button[@type='submit']")
                btn.click()
        except:
            pass

        cookies = self.browser.get_cookies()
        self.api.cookies = cookies
        soup = BeautifulSoup(self.browser.page_source, "html.parser")
        user_agent = self.browser.execute_script("return navigator.userAgent;")
        self.api.user_agent = user_agent
        try:
            script: str = soup.find_all("script", {"type": "text/javascript"})[1].get_text()
        except Exception as exc:
            raise QuotexAuthError("incorrect username or password") from exc
        finally:
            self.close()
        match = re.sub("window.settings = ", "", script.strip().replace(";", ""))

        dx: dict = json.loads(match)
        ssid = dx.get("token")

        cookiejar = requests.utils.cookiejar_from_dict({c["name"]: c["value"] for c in cookies})
        cookie_string = "; ".join([f"{c.name}={c.value}" for c in cookiejar])
        output_file = Path(".session.pkl")
        output_file.parent.mkdir(exist_ok=True, parents=True)

        data = {}
        if output_file.is_file():
            with output_file.open("rb") as file:
                data = pickle.load(file)

        data[self.email] = [{"cookies": cookie_string, "ssid": ssid, "user_agent": user_agent}]
        with output_file.open("wb") as file:
            pickle.dump(data, file)

        return ssid, cookie_string

    def close(self):
        try:
            time.sleep(0.2)
            self.browser.close()
        except:
            pass
