#!/usr/bin/env python3
"""
Enhanced test script to verify Quotex connection with all improvements
"""

import asyncio
import sys
from datetime import datetime

# Import quotex library
try:
    from quotexpy import Quotex
    QUOTEX_AVAILABLE = True
    print("✅ quotexpy library available")
except ImportError:
    print("❌ quotexpy not found. Please install: pip install quotexpy")
    QUOTEX_AVAILABLE = False
    sys.exit(1)

# Import credentials from main bot
try:
    from trading_bot_launcher import (
        QUOTEX_EMAIL, QUOTEX_PASSWORD, QUOTEX_LIVE_URL,
        connect_to_quotex, show_countdown_timer
    )
    print("✅ Successfully imported credentials and functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

async def test_enhanced_connection():
    """Test the enhanced connection function"""
    print("\n🔄 Testing Enhanced Quotex Connection...")
    print("=" * 70)
    
    try:
        # Test demo connection
        print("\n💰 Testing Demo Account Connection...")
        demo_success = await connect_to_quotex("PRACTICE", max_retries=3)
        
        if demo_success:
            print("✅ Demo connection successful!")
            
            # Test some basic operations
            try:
                from trading_bot_launcher import quotex_client
                if quotex_client:
                    # Test balance check
                    balance = await quotex_client.get_balance()
                    print(f"💰 Demo Balance: ${balance:.2f}")
                    
                    # Test asset checking
                    test_assets = ["EURUSD_otc", "GBPUSD_otc"]
                    print("📊 Testing asset availability:")
                    for asset in test_assets:
                        try:
                            asset_info = quotex_client.check_asset_open(asset)
                            if asset_info and len(asset_info) >= 3:
                                is_open = asset_info[2] if len(asset_info) > 2 else False
                                status = "✅ OPEN" if is_open else "❌ CLOSED"
                                print(f"  {asset}: {status}")
                            else:
                                print(f"  {asset}: ❓ UNKNOWN")
                        except Exception as e:
                            print(f"  {asset}: ❌ ERROR - {str(e)}")
                    
                    # Close connection
                    quotex_client.close()
                    print("🔒 Demo connection closed")
                    
            except Exception as e:
                print(f"⚠️  Error during demo operations: {str(e)}")
        else:
            print("❌ Demo connection failed")
        
        return demo_success
        
    except Exception as e:
        print(f"❌ Enhanced connection test error: {str(e)}")
        return False

async def test_countdown_timer():
    """Test the countdown timer function"""
    print("\n⏰ Testing Countdown Timer...")
    print("=" * 70)
    
    try:
        # Test short countdown
        print("Testing 5-second countdown:")
        show_countdown_timer(5, "Test countdown")
        
        print("✅ Countdown timer working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Countdown timer error: {str(e)}")
        return False

async def test_url_accessibility():
    """Test if the Quotex URL is accessible"""
    print("\n🌐 Testing URL Accessibility...")
    print("=" * 70)
    
    try:
        import requests
        
        # Test the sign-in URL
        sign_in_url = "https://market-qx.pro/en/sign-in"
        print(f"📍 Testing URL: {sign_in_url}")
        
        response = requests.get(sign_in_url, timeout=10)
        
        if response.status_code == 200:
            print("✅ URL is accessible!")
            print(f"📊 Response status: {response.status_code}")
            print(f"📏 Content length: {len(response.content)} bytes")
            
            # Check if it looks like a login page
            content = response.text.lower()
            has_email = "email" in content
            has_password = "password" in content
            has_login = any(word in content for word in ["login", "sign in", "sign-in"])
            
            print(f"🔍 Page analysis:")
            print(f"  Email field: {'✅' if has_email else '❌'}")
            print(f"  Password field: {'✅' if has_password else '❌'}")
            print(f"  Login elements: {'✅' if has_login else '❌'}")
            
            if has_email and has_password and has_login:
                print("✅ Looks like a valid login page!")
                return True
            else:
                print("⚠️  Page structure may have changed")
                return False
        else:
            print(f"❌ URL returned status code: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ URL request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to URL")
        return False
    except Exception as e:
        print(f"❌ URL test error: {str(e)}")
        return False

async def test_browser_availability():
    """Test if Chrome browser is available"""
    print("\n🌐 Testing Browser Availability...")
    print("=" * 70)
    
    try:
        import undetected_chromedriver as uc
        
        print("🔍 Testing Chrome browser availability...")
        
        # Try to create a browser instance
        options = uc.ChromeOptions()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        driver = uc.Chrome(options=options)
        
        # Test basic navigation
        driver.get("https://www.google.com")
        title = driver.title
        
        driver.quit()
        
        print("✅ Chrome browser is working!")
        print(f"📊 Test page title: {title}")
        return True
        
    except Exception as e:
        print(f"❌ Browser test error: {str(e)}")
        print("💡 Make sure Chrome browser is installed")
        return False

async def main():
    """Run all enhanced tests"""
    print("🧪 Enhanced Quotex Connection Testing")
    print("Testing all improvements and connection methods")
    print("=" * 70)
    
    results = {}
    
    try:
        # Test URL accessibility
        results['url_accessible'] = await test_url_accessibility()
        
        # Test browser availability
        results['browser_available'] = await test_browser_availability()
        
        # Test countdown timer
        results['countdown_working'] = await test_countdown_timer()
        
        # Test enhanced connection (only if prerequisites are met)
        if results['url_accessible'] and results['browser_available']:
            results['connection_success'] = await test_enhanced_connection()
        else:
            print("\n⚠️  Skipping connection test due to prerequisite failures")
            results['connection_success'] = False
        
        # Summary
        print("\n" + "=" * 70)
        print("📊 ENHANCED TEST RESULTS SUMMARY:")
        print("=" * 70)
        
        print(f"🌐 URL Accessibility: {'✅ PASS' if results['url_accessible'] else '❌ FAIL'}")
        print(f"🌐 Browser Availability: {'✅ PASS' if results['browser_available'] else '❌ FAIL'}")
        print(f"⏰ Countdown Timer: {'✅ PASS' if results['countdown_working'] else '❌ FAIL'}")
        print(f"🔗 Quotex Connection: {'✅ PASS' if results['connection_success'] else '❌ FAIL'}")
        
        # Overall status
        all_passed = all(results.values())
        
        if all_passed:
            print("\n🎉 ALL ENHANCED TESTS PASSED!")
            print("✅ Your bot is ready with all improvements:")
            print("  • Enhanced Quotex connection with retry mechanism")
            print("  • Countdown timer for next signals")
            print("  • Improved error handling and troubleshooting")
            print("  • Multiple connection configurations")
            print("  • Better URL and browser compatibility")
        else:
            print("\n⚠️  Some tests failed:")
            failed_tests = [test for test, result in results.items() if not result]
            for test in failed_tests:
                print(f"  ❌ {test}")
            
            print("\n💡 Next steps:")
            if not results['url_accessible']:
                print("  • Check internet connection")
                print("  • Try using a VPN")
                print("  • Check if Quotex is blocked in your region")
            if not results['browser_available']:
                print("  • Install Google Chrome browser")
                print("  • Update Chrome to latest version")
            if not results['connection_success']:
                print("  • Verify Quotex account credentials")
                print("  • Check account status on Quotex website")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
