#!/usr/bin/env python3
"""
Test script to verify Quotex integration - balance checking and trade execution
"""

import asyncio
import sys
from datetime import datetime

# Import quotex library
try:
    from quotexpy import Quotex
    QUOTEX_AVAILABLE = True
    print("✅ quotexpy library available")
except ImportError:
    print("❌ quotexpy not found. Please install: pip install quotexpy")
    QUOTEX_AVAILABLE = False
    sys.exit(1)

# Import credentials from main bot
try:
    from trading_bot_launcher import QUOTEX_EMAIL, QUOTEX_PASSWORD, QUOTEX_OTC_PAIRS, QUOTEX_LIVE_PAIRS
    print("✅ Successfully imported credentials and asset lists")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

async def test_demo_balance():
    """Test demo account balance checking"""
    print("\n💰 Testing Demo Account Balance...")
    
    try:
        # Connect to demo account
        demo_client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD)
        print("  🔄 Connecting to demo account...")
        
        demo_connected = await demo_client.connect()
        
        if demo_connected:
            print("  ✅ Connected to demo account")
            
            # Switch to practice mode
            demo_client.change_account("PRACTICE")
            print("  🔄 Switched to practice mode")
            
            # Get instruments
            await demo_client.get_instruments()
            print("  📊 Loaded instruments")
            
            # Get balance
            demo_balance = await demo_client.get_balance()
            print(f"  💰 Demo Balance: ${demo_balance:.2f}")
            
            # Close connection
            demo_client.close()
            print("  🔒 Demo connection closed")
            
            return demo_balance
        else:
            print("  ❌ Failed to connect to demo account")
            return None
            
    except Exception as e:
        print(f"  ❌ Demo balance test error: {str(e)}")
        return None

async def test_live_balance():
    """Test live account balance checking"""
    print("\n💰 Testing Live Account Balance...")
    
    try:
        # Connect to live account
        live_client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD)
        print("  🔄 Connecting to live account...")
        
        live_connected = await live_client.connect()
        
        if live_connected:
            print("  ✅ Connected to live account")
            
            # Switch to real mode
            live_client.change_account("REAL")
            print("  🔄 Switched to real mode")
            
            # Get instruments
            await live_client.get_instruments()
            print("  📊 Loaded instruments")
            
            # Get balance
            live_balance = await live_client.get_balance()
            print(f"  💰 Live Balance: ${live_balance:.2f}")
            
            # Close connection
            live_client.close()
            print("  🔒 Live connection closed")
            
            return live_balance
        else:
            print("  ❌ Failed to connect to live account")
            return None
            
    except Exception as e:
        print(f"  ❌ Live balance test error: {str(e)}")
        return None

async def test_asset_availability():
    """Test asset availability for trading"""
    print("\n📊 Testing Asset Availability...")
    
    try:
        # Connect to demo account for testing
        client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD)
        print("  🔄 Connecting to check asset availability...")
        
        connected = await client.connect()
        
        if connected:
            client.change_account("PRACTICE")
            await client.get_instruments()
            
            # Test OTC pairs
            print("  🔍 Checking OTC pairs availability:")
            otc_available = []
            for asset in QUOTEX_OTC_PAIRS[:5]:  # Test first 5 OTC pairs
                try:
                    asset_info = client.check_asset_open(asset)
                    if asset_info and len(asset_info) >= 3:
                        is_open = asset_info[2] if len(asset_info) > 2 else False
                        status = "✅ OPEN" if is_open else "❌ CLOSED"
                        print(f"    {asset}: {status}")
                        if is_open:
                            otc_available.append(asset)
                    else:
                        print(f"    {asset}: ❓ UNKNOWN")
                except Exception as e:
                    print(f"    {asset}: ❌ ERROR - {str(e)}")
            
            # Test Live pairs
            print("  🔍 Checking Live pairs availability:")
            live_available = []
            for asset in QUOTEX_LIVE_PAIRS[:5]:  # Test first 5 Live pairs
                try:
                    asset_info = client.check_asset_open(asset)
                    if asset_info and len(asset_info) >= 3:
                        is_open = asset_info[2] if len(asset_info) > 2 else False
                        status = "✅ OPEN" if is_open else "❌ CLOSED"
                        print(f"    {asset}: {status}")
                        if is_open:
                            live_available.append(asset)
                    else:
                        print(f"    {asset}: ❓ UNKNOWN")
                except Exception as e:
                    print(f"    {asset}: ❌ ERROR - {str(e)}")
            
            client.close()
            
            return otc_available, live_available
        else:
            print("  ❌ Failed to connect for asset checking")
            return [], []
            
    except Exception as e:
        print(f"  ❌ Asset availability test error: {str(e)}")
        return [], []

async def test_demo_trade_execution():
    """Test trade execution on demo account"""
    print("\n🎯 Testing Demo Trade Execution...")
    
    try:
        # Connect to demo account
        client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD)
        print("  🔄 Connecting for demo trade test...")
        
        connected = await client.connect()
        
        if connected:
            client.change_account("PRACTICE")
            await client.get_instruments()
            
            # Find an available OTC asset
            test_asset = None
            for asset in QUOTEX_OTC_PAIRS[:3]:
                try:
                    asset_info = client.check_asset_open(asset)
                    if asset_info and len(asset_info) >= 3 and asset_info[2]:
                        test_asset = asset
                        break
                except:
                    continue
            
            if test_asset:
                print(f"  📊 Testing with asset: {test_asset}")
                
                # Test trade parameters
                action = "call"  # or "put"
                amount = 1  # $1 for testing
                duration = 60  # 1 minute
                
                print(f"  🎯 Executing test trade: {action} ${amount} for {duration}s")
                
                # Execute trade
                success, trade_info = await client.trade(action, amount, test_asset, duration)
                
                if success:
                    print(f"  ✅ Demo trade executed successfully!")
                    print(f"  📋 Trade info: {trade_info}")
                else:
                    print(f"  ❌ Demo trade failed: {trade_info}")
                
                client.close()
                return success
            else:
                print("  ⚠️  No available assets found for testing")
                client.close()
                return False
        else:
            print("  ❌ Failed to connect for demo trade test")
            return False
            
    except Exception as e:
        print(f"  ❌ Demo trade test error: {str(e)}")
        return False

async def main():
    """Run all Quotex integration tests"""
    print("🧪 Testing Quotex Integration")
    print("=" * 60)
    
    results = {}
    
    try:
        # Test demo balance
        demo_balance = await test_demo_balance()
        results['demo_balance'] = demo_balance
        
        # Test live balance
        live_balance = await test_live_balance()
        results['live_balance'] = live_balance
        
        # Test asset availability
        otc_available, live_available = await test_asset_availability()
        results['otc_available'] = otc_available
        results['live_available'] = live_available
        
        # Test demo trade execution
        trade_success = await test_demo_trade_execution()
        results['trade_success'] = trade_success
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY:")
        print("=" * 60)
        
        if results['demo_balance'] is not None:
            print(f"✅ Demo Balance: ${results['demo_balance']:.2f}")
        else:
            print("❌ Demo Balance: Failed to retrieve")
        
        if results['live_balance'] is not None:
            print(f"✅ Live Balance: ${results['live_balance']:.2f}")
        else:
            print("❌ Live Balance: Failed to retrieve")
        
        print(f"📊 OTC Assets Available: {len(results['otc_available'])}")
        print(f"📊 Live Assets Available: {len(results['live_available'])}")
        
        if results['trade_success']:
            print("✅ Demo Trade Execution: SUCCESS")
        else:
            print("❌ Demo Trade Execution: FAILED")
        
        # Overall status
        all_tests_passed = (
            results['demo_balance'] is not None and
            results['live_balance'] is not None and
            len(results['otc_available']) > 0 and
            results['trade_success']
        )
        
        if all_tests_passed:
            print("\n🎉 ALL QUOTEX INTEGRATION TESTS PASSED!")
            print("✅ The bot can successfully:")
            print("  • Check demo account balance")
            print("  • Check live account balance") 
            print("  • Access trading assets")
            print("  • Execute trades on demo account")
        else:
            print("\n⚠️  Some tests failed. Please check the issues above.")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
