#!/usr/bin/env python3
"""
Test script to verify Quotex connection with updated URLs
"""

import asyncio
import sys
from datetime import datetime

# Import quotex library
try:
    from quotexpy import Quotex
    QUOTEX_AVAILABLE = True
    print("✅ quotexpy library available")
except ImportError:
    print("❌ quotexpy not found. Please install: pip install quotexpy")
    QUOTEX_AVAILABLE = False
    sys.exit(1)

# Import credentials from main bot
try:
    from trading_bot_launcher import QUOTEX_EMAIL, QUOTEX_PASSWORD
    print("✅ Successfully imported credentials")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

async def test_quotex_connection():
    """Test connection to Quotex with updated URLs"""
    print("\n🔄 Testing Quotex Connection with Updated URLs...")
    print("=" * 60)
    
    try:
        # Test demo connection
        print("\n💰 Testing Demo Account Connection...")
        demo_client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD, headless=True)
        print("  🔄 Connecting to demo account (headless mode)...")
        
        demo_connected = await demo_client.connect()
        
        if demo_connected:
            print("  ✅ Successfully connected to demo account!")
            
            # Switch to practice mode
            demo_client.change_account("PRACTICE")
            print("  🔄 Switched to practice mode")
            
            # Get instruments
            try:
                await demo_client.get_instruments()
                print("  📊 Successfully loaded instruments")
            except Exception as e:
                print(f"  ⚠️  Warning loading instruments: {str(e)}")
            
            # Get balance
            try:
                demo_balance = await demo_client.get_balance()
                print(f"  💰 Demo Balance: ${demo_balance:.2f}")
                demo_success = True
            except Exception as e:
                print(f"  ❌ Error getting demo balance: {str(e)}")
                demo_success = False
            
            # Close connection
            demo_client.close()
            print("  🔒 Demo connection closed")
            
        else:
            print("  ❌ Failed to connect to demo account")
            demo_success = False
            
    except Exception as e:
        print(f"  ❌ Demo connection error: {str(e)}")
        demo_success = False
    
    # Test live connection
    try:
        print("\n💰 Testing Live Account Connection...")
        live_client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD, headless=True)
        print("  🔄 Connecting to live account (headless mode)...")
        
        live_connected = await live_client.connect()
        
        if live_connected:
            print("  ✅ Successfully connected to live account!")
            
            # Switch to real mode
            live_client.change_account("REAL")
            print("  🔄 Switched to real mode")
            
            # Get instruments
            try:
                await live_client.get_instruments()
                print("  📊 Successfully loaded instruments")
            except Exception as e:
                print(f"  ⚠️  Warning loading instruments: {str(e)}")
            
            # Get balance
            try:
                live_balance = await live_client.get_balance()
                print(f"  💰 Live Balance: ${live_balance:.2f}")
                live_success = True
            except Exception as e:
                print(f"  ❌ Error getting live balance: {str(e)}")
                live_success = False
            
            # Close connection
            live_client.close()
            print("  🔒 Live connection closed")
            
        else:
            print("  ❌ Failed to connect to live account")
            live_success = False
            
    except Exception as e:
        print(f"  ❌ Live connection error: {str(e)}")
        live_success = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CONNECTION TEST RESULTS:")
    print("=" * 60)
    
    if demo_success:
        print("✅ Demo Account: Connection successful")
    else:
        print("❌ Demo Account: Connection failed")
    
    if live_success:
        print("✅ Live Account: Connection successful")
    else:
        print("❌ Live Account: Connection failed")
    
    if demo_success and live_success:
        print("\n🎉 ALL CONNECTIONS SUCCESSFUL!")
        print("✅ The bot can now connect to Quotex using market-qx.pro")
        print("✅ Both demo and live accounts are accessible")
        print("✅ Ready for trading operations")
    elif demo_success or live_success:
        print("\n⚠️  PARTIAL SUCCESS")
        print("Some connections worked, but there may be issues with credentials or account access")
    else:
        print("\n❌ ALL CONNECTIONS FAILED")
        print("Please check:")
        print("  • Internet connection")
        print("  • Quotex account credentials")
        print("  • Firewall/antivirus settings")
        print("  • VPN if required for your region")
    
    return demo_success, live_success

async def test_asset_availability():
    """Test if we can check asset availability"""
    print("\n📊 Testing Asset Availability...")
    
    try:
        client = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD, headless=True)
        connected = await client.connect()
        
        if connected:
            client.change_account("PRACTICE")
            await client.get_instruments()
            
            # Test a few OTC assets
            test_assets = ["EURUSD_otc", "GBPUSD_otc", "USDJPY_otc"]
            available_count = 0
            
            for asset in test_assets:
                try:
                    asset_info = client.check_asset_open(asset)
                    if asset_info and len(asset_info) >= 3:
                        is_open = asset_info[2] if len(asset_info) > 2 else False
                        status = "✅ OPEN" if is_open else "❌ CLOSED"
                        print(f"  {asset}: {status}")
                        if is_open:
                            available_count += 1
                    else:
                        print(f"  {asset}: ❓ UNKNOWN")
                except Exception as e:
                    print(f"  {asset}: ❌ ERROR - {str(e)}")
            
            client.close()
            
            print(f"\n📊 Available assets: {available_count}/{len(test_assets)}")
            return available_count > 0
        else:
            print("  ❌ Could not connect to test assets")
            return False
            
    except Exception as e:
        print(f"  ❌ Asset test error: {str(e)}")
        return False

async def main():
    """Run all connection tests"""
    print("🧪 Testing Quotex Connection with Updated URLs")
    print("Using market-qx.pro instead of qxbroker.com")
    print("=" * 60)
    
    try:
        # Test connections
        demo_success, live_success = await test_quotex_connection()
        
        # Test asset availability if at least one connection worked
        if demo_success or live_success:
            asset_success = await test_asset_availability()
        else:
            asset_success = False
        
        # Final summary
        print("\n" + "=" * 60)
        print("🎯 FINAL TEST SUMMARY:")
        print("=" * 60)
        
        if demo_success and live_success and asset_success:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Demo account connection: Working")
            print("✅ Live account connection: Working") 
            print("✅ Asset availability check: Working")
            print("\n🚀 Your bot is ready to use with the updated URLs!")
        else:
            print("⚠️  Some tests failed:")
            print(f"{'✅' if demo_success else '❌'} Demo account connection")
            print(f"{'✅' if live_success else '❌'} Live account connection")
            print(f"{'✅' if asset_success else '❌'} Asset availability check")
            
            if not (demo_success or live_success):
                print("\n💡 Troubleshooting tips:")
                print("  • Check your internet connection")
                print("  • Verify Quotex account credentials")
                print("  • Try using a VPN if Quotex is blocked in your region")
                print("  • Check firewall/antivirus settings")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
